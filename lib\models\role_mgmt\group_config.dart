import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_config.g.dart';

@JsonSerializable()
class GroupConfig {
  @Json<PERSON>ey(name: 'Id')
  String? id;

  @Json<PERSON><PERSON>(name: 'Name', defaultValue: '')
  String name;

  /// 系统权限分组: 1.系统权限分组 10.项目权限分组
  @JsonKey(name: 'GroupTypeenum')
  final int groupTypeenum;

  /// 系统配置 1.是 2.不是
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Defaultenum')
  final int defaultenum;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'RoleList', defaultValue: [])
  List<Role>? roleList;

  /// 是否展开（用于UI）
  bool isExpanded;

  /// 是否显示（用于UI）
  bool isVisible;

  GroupConfig({
    this.id,
    this.name = '',
    this.groupTypeenum = 1,
    this.defaultenum = 2,
    this.roleList,
    this.isExpanded = true,
    this.isVisible = false,
  });

  factory GroupConfig.fromJson(Map<String, dynamic> json) => _$GroupConfigFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GroupConfigToJson(this);
}
