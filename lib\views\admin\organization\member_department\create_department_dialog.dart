import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/components/selector/employee_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';

class CreateDepartmentDrawer extends StatefulWidget {
  final void Function()? onSuccess; // 提交成功回调
  final void Function()? onError; // 提交失败回调

  const CreateDepartmentDrawer({super.key, this.onSuccess, this.onError});

  @override
  State<CreateDepartmentDrawer> createState() => _CreateDepartmentDrawerState();
}

class _CreateDepartmentDrawerState extends State<CreateDepartmentDrawer> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  DepartmentModel departmentModel = DepartmentModel();

  /// 选中的部门列表
  List<DepartmentModel> checkedDepartments = [];

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _depNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  /// 重置数据
  void resetFormData() {
    departmentModel = DepartmentModel();
    _depNameController.text = '';
    _descriptionController.text = '';
  }

  /// 添加
  Future<void> createRequest(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    try {
      departmentModel.parentIdList = checkedDepartments.map((t) => t.id!).toList();
      final params = departmentModel.toJson();
      params.remove('Id');
      await DepartmentApi.add(params);
      ToastManager.success('提交成功');
      widget.onSuccess?.call();
      resetFormData();
      if (!isAddNext) context.pop();
    } catch (error) {
      widget.onError?.call();
    } finally {
      setState(() {
        btnLoading = false;
      });
    }
  }

  /// 打开添加部门弹窗
  void _showCreateDepartmentDrawer(BuildContext context) {
    double labelWidth = 80;

    /// 间距
    double spacing = 20;
    AppDialog.show(
      width: 480,
      context: context,
      title: '添加部门',
      isDrawer: true,
      barrierDismissible: true,
      slideDirection: SlideDirection.right,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppInput(
                  label: "部门名称",
                  labelWidth: labelWidth,
                  labelPosition: LabelPosition.left,
                  hintText: "部门名称",
                  size: InputSize.medium,
                  controller: _depNameController,
                  maxLength: 30,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入部门名称';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    departmentModel.departmentName = value;
                  },
                ),
                Row(
                  children: [
                    SizedBox(width: labelWidth, child: Text('所属部门')),
                    Expanded(
                      child: DepartmentSelector(
                        checkStrictly: true,
                        defaultCheckedDepartmentIds: checkedDepartments.map((t) => t.id!).toList(),
                        onChange: (selectedDepartments) {
                          setDialogState(() {
                            checkedDepartments = selectedDepartments;
                            print('Selected Departments: ${jsonEncode(checkedDepartments)}');
                          });
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: spacing),
                Row(
                  children: [
                    SizedBox(width: labelWidth, child: Text('部门负责人')),
                    Expanded(
                      child: EmployeeSelector(maxSelectableEmployees: 1, onChange: (value) {}),
                    ),
                  ],
                ),
                SizedBox(height: spacing),
                Row(
                  children: [
                    SizedBox(width: labelWidth, child: Text('部门HRBP')),
                    Expanded(child: EmployeeSelector()),
                  ],
                ),
                SizedBox(height: spacing),
                AppInput(
                  label: "职能描述",
                  labelPosition: LabelPosition.left,
                  controller: _descriptionController,
                  maxLines: 5,
                  hintText: "职能描述",
                  size: InputSize.medium,
                  maxLength: 3000,
                  onChanged: (value) {
                    departmentModel.description = value;
                  },
                ),
              ],
            ),
          );
        },
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          StatefulBuilder(
            builder: (context, setState) {
              return Checkbox(
                value: isAddNext,
                onChanged: (value) {
                  setState(() {
                    isAddNext = !isAddNext;
                  });
                },
              );
            },
          ),
          Text('继续新建下一条'),
          const SizedBox(width: 10),
          AppButton(text: '取消', type: ButtonType.default_, onPressed: () => context.pop()),
          const SizedBox(width: 10),
          AppButton(text: '确定', type: ButtonType.primary, onPressed: () => createRequest(context)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _depNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppButton(
      text: '添加部门',
      type: ButtonType.primary,
      loading: btnLoading,
      onPressed: () {
        _showCreateDepartmentDrawer(context);
      },
    );
  }
}
