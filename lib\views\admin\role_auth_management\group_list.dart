import 'package:flutter/material.dart';
import 'package:octasync_client/api/role_auth_management.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/role_mgmt/group_config.dart';
import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:octasync_client/views/admin/role_auth_management/create_role_dialog.dart';

class _GroupListConstants {
  static const int defaultPageSize = 9999;
  static const String loadingMessage = '加载中...';
  static const String emptyDataMessage = '暂无数据';
  static const String noSearchResultMessage = '暂无数据';
  static const String retryButtonText = '重试';
  static const String loadErrorPrefix = '加载部门数据失败: ';
}

class GroupList extends StatefulWidget {
  /// 点击分组项
  final void Function(Role role)? onItemTap;

  const GroupList({super.key, this.onItemTap});

  @override
  State<GroupList> createState() => _GroupListState();
}

class _GroupListState extends State<GroupList> {
  final GlobalKey<CreateRoleDialogState> _createRoleDialogStateKey =
      GlobalKey<CreateRoleDialogState>();

  BorderRadius itemBorderRadius = BorderRadius.circular(AppRadiusSize.radius4);
  double iconSize = 16;

  bool _isLoading = false;
  List<GroupConfig> _groups = [];
  String? _errorMessage;

  GroupConfig currentGroup = GroupConfig();

  Role currentRole = Role();

  // final Map<String, dynamic> _reqParams = {
  //   'PageIndex': 1,
  //   'PageSize': _DepartmentTreeConstants.defaultPageSize,
  // };

  // RoleAuthManagementApi.getList

  @override
  void initState() {
    super.initState();

    print('调用初始化方法');
    _getList();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsGeometry.symmetric(vertical: 10),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsetsGeometry.only(left: 16, right: 16, bottom: 10),
            child: Row(
              children: [
                AppButton(
                  text: '创建分组',
                  type: ButtonType.primary,
                  onPressed: () {
                    _handleGroupCreate(context);
                  },
                ),
                SizedBox(width: 10),

                // AppButton(
                //   text: '创建角色',
                //   type: ButtonType.primary,
                //   onPressed: () {
                //     // _handleRoleCreate(context);
                //   },
                // ),
                CreateRoleDialog(
                  key: _createRoleDialogStateKey,
                  onSuccess: () => _getList(),
                  groups: _groups,
                  child: AppButton(
                    text: '添加职级',
                    type: ButtonType.primary,
                    onPressed: () {
                      _createRoleDialogStateKey.currentState?.showCreateRoleDialog(
                        context,
                        type: DialogTypeEmun.create,
                      );
                    },
                  ),
                ),

                // btn(
                //   width: 78,
                //   title: '',
                //   onTap: () {
                //     ctx.read<RoleMgmtState>().handleGroupCreate();
                //   },
                // ),
                // btn(
                //   width: 78,
                //   title: '创建角色',
                //   onTap: () {
                //   },
                // ),
                Spacer(),
                AppButton(
                  iconData: Icons.expand,
                  size: ButtonSize.small,
                  type: ButtonType.default_,
                  onPressed: () {
                    setState(() {
                      _groups.forEach((g) => g.isExpanded = !g.isExpanded);
                    });
                  },
                ),
              ],
            ),
          ),
          Expanded(child: _buildGroupList()),
        ],
      ),
    );
  }

  Future<void> _getList() async {
    //放置重复请求
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await RoleAuthManagementApi.getList({});

      if (mounted) {
        setState(() {
          _isLoading = false;
          _groups =
              (response as List)
                  .map((e) => GroupConfig.fromJson(e as Map<String, dynamic>))
                  .toList();
        });
      }
    } catch (e) {
      _isLoading = true;
      _errorMessage = '获取数据失败：$e';
    }
  }

  Widget _buildGroupList() {
    // 错误状态
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _getList,
              child: const Text(_GroupListConstants.retryButtonText),
            ),
          ],
        ),
      );
    }

    // 加载状态
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              _GroupListConstants.loadingMessage,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ],
        ),
      );
    }

    // 空数据状态
    if (_groups.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(_GroupListConstants.emptyDataMessage, style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _groups.length,
      // itemExtent: 80,
      itemBuilder: (BuildContext ctxt, int index) {
        var group = _groups[index];
        return Container(child: Column(children: [_buildGroup(context, group)]));
      },
    );
  }

  Widget _buildGroup(BuildContext context, GroupConfig group) {
    return Container(
      child: Column(
        children: [
          _buildItem(context, '${group.name}', isGroup: true, group: group),
          if (group.roleList != null && group.roleList!.isNotEmpty && group.isExpanded)
            ..._buildItems(context, group.roleList!),
        ],
      ),
    );
  }

  List<Widget> _buildItems(BuildContext context, List<Role> roleList) {
    List<Widget> result = [];
    for (var i = 0; i < roleList.length; i++) {
      result.add(
        _buildItem(
          context,
          roleList[i].name,
          onTap: () {
            widget.onItemTap?.call(roleList[i]);
          },
        ),
      );
    }

    return result;
  }

  Widget _buildItem(
    BuildContext context,
    String text, {
    bool isGroup = false,
    GroupConfig? group,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 1, horizontal: 16),
      width: double.infinity,
      height: 36,
      child: Material(
        color: isGroup ? context.background200 : Colors.transparent,
        borderRadius: itemBorderRadius,
        child: InkWell(
          onTap: () {
            onTap?.call();
          },
          hoverColor: context.activeGrayColor.withValues(alpha: 0.5),
          splashColor: context.activeGrayColor,
          borderRadius: itemBorderRadius,
          child: Row(
            children: [
              isGroup
                  ? GestureDetector(
                    onTap: () {
                      setState(() {
                        group.isExpanded = !group.isExpanded;
                      });
                    },
                    child: AnimatedRotation(
                      key: UniqueKey(),
                      turns: group!.isExpanded ? 0 : -0.25,
                      duration: Duration(milliseconds: 300),
                      child: Icon(IconFont.mianxing_xiala, color: context.icon100, size: iconSize),
                    ),
                  )
                  : SizedBox(width: iconSize),
              Expanded(child: Text(text)),

              // 编辑、删除 按钮组
              GestureDetector(
                onTap: () {},
                child: AppDropdown(
                  placement: DropdownPlacement.bottomRight,
                  size: DropdownSize.small,
                  items: [
                    DropdownItem(text: '编辑', value: 'edit'),
                    DropdownItem(text: '删除', value: 'delete'),
                  ],
                  text: '选择选项',
                  trigger: DropdownTrigger.click, // 可选hover或click
                  child: Icon(Icons.more_vert, size: iconSize),
                  onItemSelected: (item) {
                    // 分组的编辑、删除
                    if (isGroup) {
                      if (item.value == 'edit') {
                        _handleGroupCreate(context, dialogStatus: 'edit', group: group);
                      } else if (item.value == 'delete') {
                        showDialog(
                          context: context,
                          builder:
                              (ctx) => AlertDialog(
                                title: Text('确认删除'),
                                content: Text('是否确认删除？'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(ctx),
                                    child: Text('取消'),
                                  ),
                                  TextButton(
                                    onPressed: () async {
                                      var postDatas = jsonEncode({'id': group!.id});
                                      await RoleAuthManagementApi.del(postDatas);
                                      _getList();
                                      Navigator.pop(ctx);
                                    },
                                    child: Text('删除'),
                                  ),
                                ],
                              ),
                        );
                      }
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 创建、编辑角色组
  void _handleGroupCreate(
    BuildContext context, {
    String dialogStatus = 'create',
    GroupConfig? group,
  }) {
    final TextEditingController nameController = TextEditingController();
    nameController.text = '';
    final formKey = GlobalKey<FormState>();

    print('11111111111111111');

    //如果是创建，需要添加默认值
    if (dialogStatus == 'create') {
      currentGroup = GroupConfig();
      currentGroup.id = '00000000-0000-0000-0000-000000000000';
    } else if (dialogStatus == 'edit') {
      print('调用编辑11111111');
      print(jsonEncode(group));

      ////// 需要深拷贝，方式引用修改，但是深拷贝报错，待处理~~~~~~~~~~
      //GroupConfig.fromJson(group!.toJson());
      currentGroup = group!;

      nameController.text = currentGroup.name;
    }

    AppDialog.show(
      context: context,
      title: '创建角色组',
      width: 400,
      height: 400,
      child: Form(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppInput(
                label: '角色组名称',
                controller: nameController,
                hintText: '请输入',
                onChanged: (value) {
                  currentGroup.name = value;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入角色组名称';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      onConfirm: () async {
        // 表单验证通过，执行提交操作
        if (formKey.currentState!.validate()) {
          try {
            var postDatas = jsonEncode(currentGroup);
            await RoleAuthManagementApi.add(postDatas);
            if (context.mounted) {
              context.pop();
              _getList();
            }
            ToastManager.success('保存成功');
          } catch (e) {}
        }
      },
    );
  }

  /// 创建、编辑角色
  // void _handleRoleCreate(BuildContext context, {String dialogStatus = 'create', Role? role}) {
  //   final TextEditingController nameController = TextEditingController();
  //   nameController.text = '';

  //   final formKey = GlobalKey<FormState>();

  //   //如果是创建，需要添加默认值
  //   if (dialogStatus == 'create') {
  //     currentRole = Role();
  //     currentRole.id = '00000000-0000-0000-0000-000000000000';
  //     currentRole.groupId = '0198ab66-049c-7757-8ff7-2c625a3a1193';
  //   } else if (dialogStatus == 'edit') {
  //     currentRole = Role.fromJson(role!.toJson());
  //     nameController.text = currentRole.name;
  //   }

  //   AppDialog.show(
  //     context: context,
  //     title: '创建角色',
  //     width: 400,
  //     height: 400,
  //     child: Form(
  //       key: formKey,
  //       child: Padding(
  //         padding: const EdgeInsets.symmetric(vertical: 16),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             AppInput(
  //               label: '角色名称',
  //               controller: nameController,
  //               hintText: '请输入',
  //               onChanged: (value) {
  //                 currentRole.name = value;
  //               },
  //               validator: (value) {
  //                 if (value == null || value.isEmpty) {
  //                   return '请输入角色名称';
  //                 }
  //                 return null;
  //               },
  //             ),
  //             const SizedBox(height: 16),

  //             AppSelect<String>(
  //               label: '所属分组',
  //               options:
  //                   _groups.map((g) => SelectOption<String>(value: g.id!, label: g.name)).toList(),
  //               value: currentRole.groupId,
  //               onChanged: (value) {
  //                 setState(() {
  //                   currentRole.groupId = value!;
  //                 });
  //               },
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //     onConfirm: () async {
  //       // 表单验证通过，执行提交操作
  //       if (formKey.currentState!.validate()) {
  //         try {
  //           var postDatas = jsonEncode(currentRole);
  //           await RoleAuthManagementApi.addOrEditRole(postDatas);
  //           if (context.mounted) {
  //             context.pop();
  //             _getList();
  //           }
  //           ToastManager.success('保存成功');
  //         } catch (e) {}
  //       }
  //     },
  //   );
  // }
}
